import React, { useState, useContext, useEffect } from 'react';
import {
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Alert,
    View,
    Platform,
    ActivityIndicator,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { getYearOptionsRange, getMonthOptions, getDayOptions } from '../../utils/dateUtils';
import { useForm, Controller } from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { zodResolver } from '@hookform/resolvers/zod';
import colors from '../../theme/colors';
import { MaterialIcons } from '@expo/vector-icons';
import { responsive } from '../../utils/responsive';
import LanguageContext from '../../context/LanguageContext';
import { useDispatch, useSelector } from 'react-redux';
import { createPatientThunk, createRegisterCriteriaThunk, generateTNRThunk, saveRegisterProgressThunk, validateInfantIdThunk, setFormData, fetchHospitalsThunk } from '../../features/register/registerSlice';
import { useRouter } from 'expo-router';
import ROUTES from '../../config/routes';
import { fetchPatientByTnrHospital } from '../../features/patients/patientsSlice';
import { RegistrationSchema, registrationDefaultValues } from '../../schemas/registrationSchema';
import { mapPatientDataToForm, mapFormDataToPatient, generateInfantId, generateCurrentDate, mapRegistrationResponseToForm } from '../../helpers/registration-screen/registrationHelpers';
import globalStyles from '../../styles/globalStyles';

const RegistrationForm = ({ paramTNR, paramHospital }) => {

    const [dobDay, setDobDay] = useState('');
    const [dobMonth, setDobMonth] = useState('');
    const [dobYear, setDobYear] = useState('');
    // No dropdown refs needed
    const [showIDCard, setShowIDCard] = useState(false);
    const [showPassport, setShowPassport] = useState(false);
    const [isPermanent, setIsPermanent] = useState('');
    const [permanentIdPanelChecked, setPermanentIdPanelChecked] = useState(false);
    const [isUpdateMode, setIsUpdateMode] = useState(false);

    const { t } = useContext(LanguageContext);
    const dispatch = useDispatch();
    const { selectedPatient } = useSelector(state => state.patients);
    const { loading, registerProgress, hospitals, hospitalsLoading } = useSelector(state => state.register);
    const router = useRouter();
    const handleSelectPermanent = (value) => {
        // Toggle the value if clicking the same checkbox again
        const newValue = isPermanent === value ? "null" : value;
        setIsPermanent(newValue);
        setValue('hasPermanentId', newValue, { shouldValidate: true });

        if (newValue === 'yes') {
            // Switching to Has Permanent ID: clear No Permanent branch fields
            setValue('hospital', '', { shouldValidate: false, shouldDirty: true });
            setValue('hn', '', { shouldValidate: false, shouldDirty: true });
            setValue('infantId', '', { shouldValidate: false, shouldDirty: true });
        } else if (newValue === 'no') {
            // Switching to No Permanent ID: clear Has Permanent branch fields
            setValue('permanentInfantId', '', { shouldValidate: false, shouldDirty: true });
            setValue('passport', '', { shouldValidate: false, shouldDirty: true });
            setPermanentIdPanelChecked(false);
        } else {
            // Both checkboxes are now unchecked
            setValue('hospital', '', { shouldValidate: false, shouldDirty: true });
            setValue('hn', '', { shouldValidate: false, shouldDirty: true });
            setValue('infantId', '', { shouldValidate: false, shouldDirty: true });
            setValue('permanentInfantId', '', { shouldValidate: false, shouldDirty: true });
            setValue('passport', '', { shouldValidate: false, shouldDirty: true });
        }
        // Clear all validation errors so UI is fresh for the selected branch
        if (clearErrors) {
            clearErrors();
        }
    };

    const logFormError = (formErrors) => {
        const first = Object.keys(formErrors || {})[0];
        if (!first) {
            // console.log('[Form Error] None')
            return;
        }
        const err = formErrors[first];
        const msg = err?.message || (typeof err === 'string' ? err : JSON.stringify(err));
        // console.log(`[Form Error] ${first}: ${msg}`);
    };

    useEffect(() => {
        if (paramTNR && paramHospital) {
            setIsUpdateMode(true);
            dispatch(fetchPatientByTnrHospital({ TNR: paramTNR, hospital: paramHospital }));
        }
    }, [paramTNR, paramHospital]);

    useEffect(() => {
        if (isUpdateMode && selectedPatient) {
            // Use the new mapping function that follows Section 2 pattern
            mapRegistrationResponseToForm(selectedPatient, {
                setValue,
                setDobDay,
                setDobMonth,
                setDobYear,
                setShowIDCard,
                setShowPassport,
                setIsPermanent,
            });

            // Trigger validation after data is loaded
            setTimeout(() => {
                trigger();
            }, 100);
        }
    }, [selectedPatient, isUpdateMode, setValue, setDobDay, setDobMonth, setDobYear, setShowIDCard, setShowPassport, setIsPermanent, trigger]);



    const {
        control,
        handleSubmit,
        formState: { errors },
        reset,
        clearErrors,
        setValue,
        trigger
    } = useForm({
        resolver: zodResolver(RegistrationSchema),
        defaultValues: registrationDefaultValues,
    });

    const submitForm = async (data) => {
        try {
            const normalized = { ...data };
            if (isPermanent) {
                normalized.infantId = data.permanentInfantId || '';
            }

            // Store form data in Redux
            dispatch(setFormData(normalized));

            let tnrToUse;

            if (isUpdateMode) {
                tnrToUse = paramTNR;
            } else {
                const dateCreate = generateCurrentDate();
                const tnrResult = await dispatch(generateTNRThunk(dateCreate)).unwrap();
                tnrToUse = tnrResult?.data?.TNR || "";
            }

            const patientData = mapFormDataToPatient(normalized, isPermanent);
            if (!isUpdateMode) {
                // With no Infant ID input, skip ID validation. If an ID is provided in data anyway, validate only then.
                if (isPermanent && patientData.infant_id) {
                    const validateResult = await dispatch(validateInfantIdThunk({
                        infantId: patientData.infant_id,
                        TNR: tnrToUse
                    })).unwrap();

                    if (validateResult.result !== "pass") {
                        Alert.alert('Info', "หมายเลข Infant's ID Number ซ้ำ");
                        return;
                    }
                }

                await dispatch(createRegisterCriteriaThunk()).unwrap();
                await dispatch(createPatientThunk(patientData)).unwrap();
                await dispatch(
                    saveRegisterProgressThunk({
                        TNR: tnrToUse,
                        temporary: isPermanent ? '' : patientData.infant_id,
                        permanent: isPermanent ? patientData.infant_id : ''
                    })
                ).unwrap();
                Alert.alert('Success', isUpdateMode ? 'Patient data updated!' : 'Registration completed!');
                router.replace(ROUTES.PRIVATE.CONFIRM_REGISTRATION_SCREEN);
            } else {
                Alert.alert('Success', isUpdateMode ? 'Patient data updated!' : 'Registration completed!');
                // Update existing patient - you'll need to create an update thunk
                // await dispatch(updatePatientThunk({ TNR: tnrToUse, ...patientData })).unwrap();
            }



        } catch (err) {
            Alert.alert('Error', err.message || (isUpdateMode ? 'Update failed' : 'Registration failed'));
        }
    };
    return (
        <KeyboardAwareScrollView
            contentContainerStyle={styles.content}
            extraScrollHeight={Platform.OS === 'android' ? 120 : 0}
            enableOnAndroid={true}
            enableResetScrollToCoords={Platform.OS === 'ios' ? false : true}
        >
            {/* Infant Details */}
            <Text style={styles.sectionTitle}>{t.t('Infant (patient) information')}</Text>

            {/* Permanent ID presence checkboxes */}
            <View style={{ marginBottom: 10 }}>
                <TouchableOpacity
                    style={styles.checkboxContainer}
                    onPress={() => handleSelectPermanent("yes")}
                >
                    <View style={[globalStyles.checkboxBox, isPermanent === 'yes' && globalStyles.checkboxChecked]}>
                        {isPermanent === 'yes' && <Text style={globalStyles.checkmark}>✓</Text>}
                    </View>
                    <Text style={styles.checkboxLabel}>{t.t('Has Permanent ID Number')}</Text>
                    <MaterialIcons name="info-outline" size={16} color="#9ec8c8" style={{ marginLeft: 8 }} />
                </TouchableOpacity>
                {isPermanent == "yes" && (
                    <View style={styles.permanentPanel}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
                            <Text style={styles.permanentPanelTitle}>{t.t("Infant's ID Number")}</Text>
                        </View>

                        {/* Infant's 13-digit ID (manual entry for Has Permanent path) */}
                        <View style={{ marginBottom: 12 }}>
                            <Text style={styles.inputLabel}>{t.t('13-digit ID')}</Text>
                            <Controller
                                control={control}
                                name="permanentInfantId"
                                render={({ field: { onChange, value } }) => (
                                    <TextInput
                                        style={[styles.input, errors.permanentInfantId && styles.inputError]}
                                        placeholder={t.t("Infant’s ID Number")}
                                        value={value}
                                        onChangeText={onChange}
                                        keyboardType="number-pad"
                                        maxLength={13}
                                    />
                                )}
                            />
                            {errors.permanentInfantId && <Text style={styles.errorText}>{errors.permanentInfantId.message}</Text>}
                        </View>

                        {/* Passport number */}
                        <View>
                            <Text style={styles.inputLabel}>{t.t('Passport Number')}</Text>
                            <Controller
                                control={control}
                                name="passport"
                                render={({ field: { onChange, value } }) => (
                                    <TextInput
                                        style={[styles.input, errors.passport && styles.inputError]}
                                        placeholder={t.t('Passport Number')}
                                        value={value}
                                        onChangeText={onChange}
                                    />
                                )}
                            />
                            {errors.passport && <Text style={styles.errorText}>{errors.passport.message}</Text>}
                            {/* Permanent ID number checkbox under Passport */}
                            <TouchableOpacity
                                style={[styles.checkboxContainer, { marginTop: 12 }]}
                                onPress={() => setPermanentIdPanelChecked((prev) => !prev)}
                            >
                                <View style={[globalStyles.checkboxBox, permanentIdPanelChecked && globalStyles.checkboxChecked]}>
                                    {permanentIdPanelChecked && <Text style={globalStyles.checkmark}>✓</Text>}
                                </View>
                                <Text style={styles.checkboxLabel}>{t.t('Permanent ID number')}</Text>
                                <MaterialIcons name="info-outline" size={16} color="#9ec8c8" style={{ marginLeft: 8 }} />
                            </TouchableOpacity>
                        </View>
                    </View>
                )}

                <TouchableOpacity
                    style={styles.checkboxContainer}
                    onPress={() => handleSelectPermanent("no")}
                >
                    <View style={[globalStyles.checkboxBox, isPermanent === 'no' && globalStyles.checkboxChecked]}>
                        {isPermanent === 'no' && <Text style={globalStyles.checkmark}>✓</Text>}
                    </View>
                    <Text style={styles.checkboxLabel}>{t.t('No Permanent ID Number')}</Text>
                    <MaterialIcons name="info-outline" size={16} color="#9ec8c8" style={{ marginLeft: 8 }} />
                </TouchableOpacity>

                {/* Error for mandatory selection between Permanent vs No Permanent */}
                {errors.hasPermanentId && (
                    <Text style={styles.errorText}>{errors.hasPermanentId.message}</Text>
                )}

                {/* Panel shown when No Permanent ID is selected */}
                {isPermanent == "no" && (
                    <View style={styles.permanentPanel}>
                        {/* Select Hospital */}
                        <View style={{ marginBottom: 12 }}>
                            <Text style={styles.inputLabel}>{t.t('Select Hospital')}</Text>
                            <Controller
                                control={control}
                                name="hospital"
                                render={({ field: { onChange, value } }) => (
                                    <Dropdown
                                        style={[styles.input, errors.hospital && styles.inputError]}
                                        data={(hospitals || []).map((h) => {
                                            const name = h?.hospital_name || h?.name || '';
                                            const number = h?.hospital_number || h?.number || '';
                                            let label = '';
                                            if (name && number) label = `${name} (${number})`;
                                            else if (name) label = name;
                                            else if (number) label = `${number}`;
                                            else label = '';
                                            return { label, value: name, meta: { name, number } };
                                        })}
                                        labelField="label"
                                        valueField="value"
                                        placeholder={hospitalsLoading ? 'Loading...' : t.t('Select Hospital')}
                                        value={value}
                                        onFocus={() => {
                                            try { dispatch(fetchHospitalsThunk({ excludeSelf: true })); } catch (_) { }
                                        }}
                                        onChange={(item) => {
                                            const selectedName = item?.meta?.name ?? '';
                                            const selectedNumber = item?.meta?.number || '';
                                            onChange(selectedName || '');
                                            setValue('hn', selectedNumber, { shouldValidate: true, shouldDirty: true });
                                            setValue('hnOptional', selectedNumber, { shouldValidate: true, shouldDirty: true });
                                            const iid = generateInfantId(selectedNumber);
                                            setValue('infantId', iid, { shouldValidate: true, shouldDirty: true });
                                        }}
                                    />
                                )}
                            />
                            {errors.hospital && <Text style={styles.errorText}>{errors.hospital.message}</Text>}
                        </View>

                        {/* HN */}
                        <View style={{ marginBottom: 12 }}>
                            <Text style={styles.inputLabel}>{t.t('HN')}</Text>
                            <Controller
                                control={control}
                                name="hnOptional"
                                render={({ field: { onChange, value } }) => (
                                    <TextInput
                                        style={[styles.input, errors.hn && styles.inputError]}
                                        placeholder={t.t('HN')}
                                        value={value}
                                        onChangeText={onChange}
                                    />
                                )}
                            />
                            {errors.hnOptional && <Text style={styles.errorText}>{errors.hnOptional.message}</Text>}
                        </View>

                        {/* Infant's ID Number (disabled) */}
                        <View>
                            <Text style={styles.inputLabel}>{t.t("Infant's ID Number")}</Text>
                            <Controller
                                control={control}
                                name="infantId"
                                render={({ field: { value } }) => (
                                    <TextInput
                                        style={[styles.input, { opacity: 0.6 }]}
                                        placeholder={t.t("Infant’s ID Number")}
                                        value={value}
                                        editable={false}
                                    />
                                )}
                            />
                            {errors.infantId && <Text style={styles.errorText}>{errors.infantId.message}</Text>}
                        </View>
                    </View>
                )}
            </View>



            {/* Date of Birth */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Date of birth')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            {(() => {
                // Years: currentYear - 4 to currentYear + 10; months 1-12; days depend on selected month/year
                const years = getYearOptionsRange(4, 10);
                const months = getMonthOptions();
                const days = getDayOptions(dobYear, dobMonth);
                // Changing key forces Day dropdown to remount and refresh its internal state
                const dayDropdownKey = `${dobYear || '____'}-${dobMonth || '__'}`;

                // Prepend non-selectable headers inside dropdown lists
                const dayItems = [{ label: t.t('Day'), value: '__HEADER_DAY__', header: true, disable: true }, ...days];
                const monthItems = [{ label: t.t('Month'), value: '__HEADER_MONTH__', header: true, disable: true }, ...months];
                const yearItems = [{ label: t.t('Year'), value: '__HEADER_YEAR__', header: true, disable: true }, ...years];

                const renderDropdownItem = (item) => {
                    if (item.header) {
                        return (
                            <View style={styles.dropdownHeaderItemContainer}>
                                <Text style={styles.dropdownHeaderItemText}>{item.label}</Text>
                            </View>
                        );
                    }
                    return (
                        <View style={styles.dropdownItemContainer}>
                            <Text style={styles.dropdownItemText}>{item.label}</Text>
                        </View>
                    );
                };

                const updateDob = (y, m, d) => {
                    const yy = (y || dobYear || '').toString().padStart(4, '');
                    const mm = (m || dobMonth || '').toString().padStart(2, '');
                    const dd = (d || dobDay || '').toString().padStart(2, '');
                    if (yy && mm && dd) {
                        setValue('dob', `${yy}-${mm}-${dd}`);
                    }
                };

                return (
                    <View style={{ flexDirection: 'row', gap: 10 }}>
                        <View style={{ flex: 1 }}>
                            <Dropdown
                                key={dayDropdownKey}
                                data={dayItems}
                                labelField="label"
                                valueField="value"
                                placeholder={t.t('Day')}
                                value={dobDay || null}
                                style={[styles.input]}
                                renderItem={renderDropdownItem}
                                onChange={(item) => {
                                    if (item.header) return;
                                    setDobDay(item.value);
                                    updateDob(undefined, undefined, item.value);
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <Dropdown
                                data={monthItems}
                                labelField="label"
                                valueField="value"
                                placeholder={t.t('Month')}
                                value={dobMonth || null}
                                style={[styles.input]}
                                renderItem={renderDropdownItem}
                                onChange={(item) => {
                                    if (item.header) return;
                                    const newMonth = item.value;
                                    setDobMonth(newMonth);
                                    // If current day exceeds new month's max days, reset it and clear composed dob
                                    const maxDaysLen = getDayOptions(dobYear, newMonth).length;
                                    if (dobDay && parseInt(dobDay, 10) > maxDaysLen) {
                                        setDobDay('');
                                        setValue('dob', '');
                                    }
                                    updateDob(undefined, newMonth, undefined);
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <Dropdown
                                data={yearItems}
                                labelField="label"
                                valueField="value"
                                placeholder={t.t('Year')}
                                value={dobYear || null}
                                style={[styles.input]}
                                renderItem={renderDropdownItem}
                                onChange={(item) => {
                                    if (item.header) return;
                                    const newYear = item.value;
                                    setDobYear(newYear);
                                    // If current day exceeds new year's Feb days etc., reset it and clear composed dob
                                    const maxDaysLen = getDayOptions(newYear, dobMonth).length;
                                    if (dobDay && parseInt(dobDay, 10) > maxDaysLen) {
                                        setDobDay('');
                                        setValue('dob', '');
                                    }
                                    updateDob(newYear, undefined, undefined);
                                }}
                            />
                        </View>
                    </View>
                );
            })()}

            {errors.dob && <Text style={styles.errorText}>{errors.dob.message}</Text>}

            {/* Gender */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Gender')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="sex"
                render={({ field: { value, onChange } }) => (
                    <View style={styles.genderContainer}>
                        <TouchableOpacity
                            style={[
                                styles.genderButton,
                                value === 'Male' && styles.genderSelected,
                                styles.genderLeft,
                            ]}
                            onPress={() => onChange('Male')}
                        >
                            <Text style={value === 'Male' ? styles.genderTextSelected : styles.genderText}>{t.t('Male')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.genderButton,
                                value === 'Female' && styles.genderSelected,
                                styles.genderRight,
                            ]}
                            onPress={() => onChange('Female')}
                        >
                            <Text style={value === 'Female' ? styles.genderTextSelected : styles.genderText}>{t.t('Female')}</Text>
                        </TouchableOpacity>
                    </View>
                )}
            />
            {errors.sex && <Text style={styles.errorText}>{errors.sex.message}</Text>}

            {/* Infant Name */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Name')}</Text>
            </View>
            <Controller
                control={control}
                name="infantName"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={[
                            styles.input,
                            errors.infantName && styles.inputError,
                        ]}
                        placeholder={t.t('Enter Name')}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />
            {errors.infantName && <Text style={styles.errorText}>{errors.infantName.message}</Text>}

            {/* Hospital Number */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Hospital Number (HN)')}</Text>
            </View>
            <Controller
                control={control}
                name="hn"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={[
                            styles.input,
                            errors.hn && styles.inputError,
                        ]}
                        placeholder={t.t('Enter Hospital Number (HN)')}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />
            {errors.hn && <Text style={styles.errorText}>{errors.hn.message}</Text>}

            {/* No Infant ID input in this version */}

            {/* Ethnic */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Ethnic')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="ethnic"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={[
                            styles.input,
                            errors.ethnic && styles.inputError,
                        ]}
                        placeholder={t.t('Ethnic')}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />
            {errors.ethnic && <Text style={styles.errorText}>{errors.ethnic.message}</Text>}

            {/* Mother Info */}
            <Text style={styles.sectionTitle}>{t.t('Mother information')}</Text>

            <Text style={styles.inputLabel}>{t.t('Mother’s Name')}</Text>
            <Text style={styles.optionalText}>{t.t('Enter the name in Thai or English as per the ID')}</Text>
            <Controller
                control={control}
                name="motherName"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={[
                            styles.input,
                            errors.motherName && styles.inputError,
                        ]}
                        placeholder={t.t('Enter Mother’s Name')}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />
            {errors.motherName && <Text style={styles.errorText}>{errors.motherName.message}</Text>}

            <Text style={styles.inputLabel}>{t.t('Mother’s ID number')}</Text>
            <Text style={styles.optionalText}>{t.t('Thai people use ID card numbers, foreigners use passport numbers')}</Text>

            {/* Checkbox for ID Card */}
            <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setShowIDCard(!showIDCard)}
            >
                <View style={[globalStyles.checkboxBox, showIDCard && globalStyles.checkboxChecked]}>
                    {showIDCard && <Text style={globalStyles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>{t.t('ID Card')}</Text>
            </TouchableOpacity>

            {showIDCard && (
                <Controller
                    control={control}
                    name="idCard"
                    render={({ field: { onChange, value } }) => (
                        <TextInput
                            style={styles.input}
                            placeholder={t.t('ID card')}
                            value={value}
                            onChangeText={onChange}
                        />
                    )}
                />
            )}

            {/* Checkbox for Passport */}
            <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setShowPassport(!showPassport)}
            >
                <View style={[globalStyles.checkboxBox, showPassport && globalStyles.checkboxChecked]}>
                    {showPassport && <Text style={globalStyles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>{t.t('Passport No')}</Text>
            </TouchableOpacity>

            {showPassport && (
                <Controller
                    control={control}
                    name="passport"
                    render={({ field: { onChange, value } }) => (
                        <TextInput
                            style={styles.input}
                            placeholder={t.t('Passport No')}
                            value={value}
                            onChangeText={onChange}
                        />
                    )}
                />
            )}


            {/* Address */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Address')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="address"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={styles.textArea}
                        placeholder={t.t('Address')}
                        value={value}
                        onChangeText={onChange}
                        multiline
                    />
                )}
            />

            {/* Telephone */}
            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Telephone number')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="telephone"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={[
                            styles.input,
                            errors.telephone && styles.inputError,
                        ]}
                        placeholder={`${t.t('Telephone number')} (e.g. 06/08/09xxxxxxxx)`}
                        keyboardType="phone-pad"
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />
            {errors.telephone && <Text style={styles.errorText}>{errors.telephone.message}</Text>}

            {/* Contact Person */}
            <Text style={styles.sectionTitle}>{t.t('Contact person')}</Text>

            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Name')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="contactName"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={styles.input}
                        placeholder={t.t('Contact Name')}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />

            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Relationship')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="contactRelationship"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={styles.input}
                        placeholder={t.t('Relationship')}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />

            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Telephone number')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Controller
                control={control}
                name="contactPhone"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={[
                            styles.input,
                            errors.contactPhone && styles.inputError,
                        ]}
                        placeholder={`${t.t('Contact Telephone')} (e.g. 06/08/09xxxxxxxx)`}
                        keyboardType="phone-pad"
                        value={value}
                        onChangeText={onChange}
                        maxLength={10}
                    />
                )}
            />
            {errors.contactPhone && <Text style={styles.errorText}>{errors.contactPhone.message}</Text>}

            <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>{t.t('Other Contact')}</Text>
                <Text style={styles.optionalText}> - {t.t('Optional')}</Text>
            </View>
            <Text style={styles.optionalText}>{`${t.t('eg:email')},${t.t('line ID')}`}</Text>
            <Controller
                control={control}
                name="otherContact"
                render={({ field: { onChange, value } }) => (
                    <TextInput
                        style={styles.input}
                        placeholder={`${t.t('eg:email')},${t.t('line ID')}`}
                        value={value}
                        onChangeText={onChange}
                    />
                )}
            />

            {/* Submit */}
            <TouchableOpacity
                style={[styles.button, loading && styles.buttonDisabled]}
                onPress={handleSubmit(
                    submitForm,
                    (formErrors) => {
                        logFormError(formErrors);
                    }
                )}
                disabled={loading}
            >
                {loading ? (
                    <ActivityIndicator color="#fff" size="small" />
                ) : (
                    <Text style={styles.buttonText}>
                        {isUpdateMode ? t.t('Update Data') : t.t('Save and Generate TNR No')}
                    </Text>
                )}
            </TouchableOpacity>
        </KeyboardAwareScrollView>
    );
};


const styles = StyleSheet.create({
    content: {
        padding: responsive(16),
        paddingBottom: responsive(8)
    },
    sectionTitle: {
        fontWeight: 'bold',
        fontSize: 16,
        marginTop: 20,
        marginBottom: 10,
        borderBottomWidth: 1,
        paddingBottom: responsive(8),
        borderBottomColor: colors.border.gray
    },
    labelRow: {
        flexDirection: 'row',
        marginTop: 10,
        marginBottom: 4
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4
    },
    debugBox: {
        marginTop: 16,
        padding: 12,
        backgroundColor: '#fff4f4',
        borderColor: '#f5c6cb',
        borderWidth: 1,
        borderRadius: 6,
    },
    debugTitle: {
        fontWeight: 'bold',
        color: '#721c24',
        marginBottom: 6,
    },
    debugText: {
        fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
        fontSize: 12,
        color: '#721c24',
    },
    optionalText: {
        fontSize: 14,
        color: '#888',
        fontStyle: 'italic',
        marginBottom: 4
    },
    input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        padding: responsive(12),
        backgroundColor: '#fff',
        marginBottom: 10
    },
    textArea: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        padding: responsive(12),
        height: 80,
        textAlignVertical: 'top',
        backgroundColor: '#fff',
        marginBottom: 10
    },
    genderContainer: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        overflow: 'hidden',
        marginBottom: 10
    },
    genderButton: {
        flex: 1,
        padding: responsive(12),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#eee'
    },
    genderSelected: {
        // backgroundColor: '#5AC2B3'
        backgroundColor: colors.button.primary
    },
    genderLeft: {
        borderRightWidth: 1,
        borderRightColor: '#ccc'
    },
    genderRight: {
        // no border needed here
    },
    genderText: {
        color: '#000'
    },
    genderTextSelected: {
        color: '#fff',
        fontWeight: 'bold'
    },
    checkboxRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 4
    },
    subtext: {
        color: '#aaa',
        fontSize: 12,
        marginBottom: 8
    },
    button: {
        backgroundColor: colors.button.primary,
        padding: responsive(14),
        borderRadius: 25,
        alignItems: 'center',
        marginTop: 30
    },
    buttonText: {
        color: '#fff',
        fontWeight: 'bold'
    },
    errorText: { color: 'red', fontSize: 12, marginBottom: 8 },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 5,
    },
    checkbox: {
        width: 22,
        height: 22,
        borderWidth: 2,
        borderColor: '#999',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8,
        borderRadius: 4,
        backgroundColor: '#fff',
    },
    checkedBox: {
        backgroundColor: '#fff',
        borderColor: '#4FB6A0',
    },
    checkmark: {
        color: '#4FB6A0',
        fontSize: 16,
        fontWeight: 'bold',
    },
    checkboxLabel: {
        fontSize: 16,
    },
    permanentPanel: {
        backgroundColor: '#f2f2f2',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e0e0e0',
        padding: responsive(12),
        marginTop: 8,
        marginBottom: 12,
    },
    permanentPanelTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    buttonDisabled: {
        opacity: 0.6,
    },
    dropdownHeaderItemContainer: {
        paddingVertical: 6,
        paddingHorizontal: 12,
        backgroundColor: '#f6f6f6',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    dropdownHeaderItemText: {
        fontSize: 12,
        color: '#666',
    },
    dropdownItemContainer: {
        paddingVertical: 12,
        paddingHorizontal: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    dropdownItemText: {
        fontSize: 14,
    },
});

export default RegistrationForm;
