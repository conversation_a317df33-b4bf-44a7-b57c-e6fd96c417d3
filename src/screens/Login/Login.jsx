import React, { useState, useContext, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ActivityIndicator,
    SafeAreaView,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    Dimensions,
    Image
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'expo-router';
import { loginUser } from '../../features/auth/authSlice';
import LanguageContext from '../../context/LanguageContext';
import logoMain from '../../assets/img/logo_main.png'
const { width } = Dimensions.get('window');

const Login = () => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const { t, locale, updateLanguage } = useContext(LanguageContext);
    const { login, loading, error, isAuthenticated } = useSelector((state) => state.auth)
    const dispatch = useDispatch();
    const router = useRouter();

    useEffect(() => {
        if (isAuthenticated) {
            router.replace('/privateRoutes/home');
        }
    }, [isAuthenticated]);

    const handleLogin = () => {
        dispatch(loginUser({ username, password }));
    };

    const handleLanguageSwitch = () => {
        updateLanguage(locale === 'en' ? 'th' : 'en');
    };

    const handleForgotPassword = () => {
        router.push('/publicRoutes/forgotPassword');
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            >
                <View style={styles.container}>
                    <View style={styles.headerContainer}>
                        {/* <View style={styles.logoCircle} /> */}
                        <Image source={logoMain} style={styles.logoImage} resizeMode="contain" />
                        <Text style={styles.appName}>ThaiNY</Text>
                        <Text style={styles.subtitle}>
                            Thai National <Text style={styles.subtitleBold}>NewBorn</Text> Registry
                        </Text>
                    </View>
                    <TouchableOpacity
                        style={{ alignSelf: 'flex-end', margin: 10, padding: 8, backgroundColor: '#e0e0e0', borderRadius: 8 }}
                        onPress={handleLanguageSwitch}
                        activeOpacity={0.7}
                    >
                        <Text>{locale === 'en' ? 'ไทย' : 'English'}</Text>
                    </TouchableOpacity>
                    {error && (
                        <Text style={{ color: 'red', textAlign: 'center', marginBottom: 8 }}>{error.message || error}</Text>
                    )}
                    <View style={styles.inputContainer}>
                        <TextInput
                            style={styles.input}
                            placeholder={t.t('Username')}
                            placeholderTextColor="#bdbdbd"
                            value={username}
                            onChangeText={setUsername}
                            autoCapitalize="none"
                        />
                        <TextInput
                            style={styles.input}
                            placeholder={t.t('password')}
                            placeholderTextColor="#bdbdbd"
                            value={password}
                            onChangeText={setPassword}
                            secureTextEntry
                        />
                    </View>
                    <TouchableOpacity
                        style={styles.loginButton}
                        onPress={handleLogin}
                        disabled={loading}
                        activeOpacity={0.8}
                    >
                        {loading ? (
                            <ActivityIndicator color="#fff" />
                        ) : (
                            <Text style={styles.loginButtonText}>{t.t('Login')}</Text>
                        )}
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.forgotButton} activeOpacity={0.7} onPress={handleForgotPassword}>
                        <Text style={styles.forgotButtonText}>{t.t('Forgot password')}</Text>
                    </TouchableOpacity>
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: '7%',
        backgroundColor: '#f5f5f5',
    },
    headerContainer: {
        alignItems: 'flex-start',
        justifyContent: 'center',
        marginBottom: 24,
        width: '100%',
        maxWidth: 420,
        alignSelf: 'center',
        paddingLeft: '10%',
    },
    logoCircle: {
        width: width * 0.18,
        height: width * 0.18,
        borderRadius: (width * 0.18) / 2,
        backgroundColor: '#8ac7c8',
        marginBottom: 8,
    },
    appName: {
        fontSize: 32,
        fontWeight: 'bold',
        color: '#6dc3c6',
        marginBottom: 4,
    },
    subtitle: {
        fontSize: 16,
        color: '#7dbdc2',
        marginBottom: 32,
    },
    subtitleBold: {
        fontWeight: 'bold',
        color: '#6dc3c6',
    },
    inputContainer: {
        width: '100%',
        maxWidth: 420,
        marginBottom: 16,
    },
    input: {
        backgroundColor: '#fff',
        borderRadius: 24,
        paddingHorizontal: 20,
        paddingVertical: 12,
        fontSize: 16,
        marginBottom: 12,
        borderWidth: 0,
        elevation: 1,
        shadowColor: '#000',
        shadowOpacity: 0.03,
        shadowRadius: 2,
        color:"black",
        shadowOffset: { width: 0, height: 1 },
    },
    loginButton: {
        width: '100%',
        maxWidth: 420,
        backgroundColor: '#8ac7c8',
        borderRadius: 24,
        paddingVertical: 13,
        alignItems: 'center',
        marginBottom: 18,
    },
    loginButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
    forgotButton: {
        width: '100%',
        maxWidth: 420,
        borderColor: '#8ac7c8',
        borderWidth: 1,
        borderRadius: 24,
        paddingVertical: 13,
        alignItems: 'center',
        marginTop: 4,
    },
    forgotButtonText: {
        color: '#8ac7c8',
        fontSize: 16,
        fontWeight: '400',
    },
    logoImage: {
        width: 80,
        height: 80,
        marginBottom: 12,
    },

});

export default Login; 