

export const generateInfantId = (hospitalNumber = '') => {
    // Format: <hospitalNumber>-YYYYMMDD-SSS
    const hnPart = String(hospitalNumber || '').trim();
    const d = new Date();
    const y = d.getFullYear();
    const m = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const seq = String(d.getTime()).slice(-3);
    return `${hnPart}-${y}${m}${day}-${seq}`;
};

// Helper to filter out empty values
const clean = obj => {
    const result = {};
    Object.entries(obj).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
            result[key] = value;
        }
    });
    return result;
};

export const mapFormDataToPatient = (formData, isPermanent) => {
    const isPermanentFlag = isPermanent?.toLowerCase() === 'yes';
    const infantIdSource = isPermanent ? formData.permanentInfantId : formData.infantId;
    const infant_id = infantIdSource ? `${infantIdSource}ε` : undefined;
    const base = clean({
        fullname: formData.infantName,
        HN: isPermanentFlag ? undefined : formData.hn,
        infant_id,
        passport_id: formData.passport,
        illegal: 'No',
        DOB: formData.dob,
        sex: formData.sex,
        ethnic: formData.ethnic,
        mother_fullname: formData.motherName,
        mother_id: formData.idCard,
        mother_passport: formData.passport,
        mother_address: formData.address,
        mother_tel: formData.telephone,
        contact_person_name: formData.contactName,
        relation: formData.contactRelationship,
        contact_person_tel: formData.contactPhone,
        other_contact: formData.otherContact,
        status: 'active',
        isPermanent,
    });

    return clean({
        ...base,
        ...(!isPermanentFlag && formData.hospital ? { hospital: formData.hospital } : {})
    });
};

export const generateCurrentDate = () => {
    const dateObj = new Date();
    const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getUTCDate()).padStart(2, '0');
    const year = dateObj.getUTCFullYear();
    return `${year}-${month}-${day}`;
};

export const mapRegistrationResponseToForm = (data, setters = {}) => {
    if (!data) return {};

    const {
        setValue,
        setDobDay,
        setDobMonth,
        setDobYear,
        setShowIDCard,
        setShowPassport,
        setIsPermanent,
        hospitals, // Add hospitals list to find matching hospital
    } = setters;

    // --- Basic Patient Information ---
    if (data.fullname) {
        setValue('infantName', data.fullname, { shouldValidate: true });
    }

    // --- Hospital and HN Logic ---
    if (data.hospital) {
        setValue('hospital', data.hospital, { shouldValidate: true });

        // If we have hospitals list, try to find matching hospital and set HN accordingly
        if (hospitals && Array.isArray(hospitals)) {
            const matchingHospital = hospitals.find(h =>
                (h?.hospital_name === data.hospital || h?.name === data.hospital)
            );

            if (matchingHospital) {
                const hospitalNumber = matchingHospital?.hospital_number || matchingHospital?.number || '';
                if (hospitalNumber) {
                    setValue('hn', hospitalNumber, { shouldValidate: true });
                    setValue('hnOptional', hospitalNumber, { shouldValidate: true });
                }
            }
        }
    }

    // If HN is provided directly (fallback)
    if (data.HN) {
        setValue('hn', data.HN, { shouldValidate: true });
        setValue('hnOptional', data.HN, { shouldValidate: true });
    }

    // --- Infant ID and Permanent ID Logic ---
    if (data.infant_id) {
        const cleanId = data.infant_id.replace(/ε$/, "");
        setValue('infantId', cleanId, { shouldValidate: true });
        setValue('permanentInfantId', cleanId, { shouldValidate: true });

        // Determine if it's a permanent ID based on the presence of 'ε' suffix
        const hasPermanentId = data.infant_id.includes("ε") ? "yes" : "no";
        setValue('hasPermanentId', hasPermanentId, { shouldValidate: true });
        setIsPermanent?.(hasPermanentId);
    } else {
        // Handle illegal status for non-permanent IDs
        if (data.illegal === "No") {
            setValue('hasPermanentId', 'no', { shouldValidate: true });
            setIsPermanent?.('no');

            // For non-permanent IDs, generate infant ID based on hospital number if available
            if (data.HN) {
                const generatedId = generateInfantId(data.HN);
                setValue('infantId', generatedId, { shouldValidate: true });
            }
        }
    }

    // --- Passport (prioritize infant passport, fallback to mother passport) ---
    if (data.infant_id?.includes("ε") && data.passport_id) {
         console.log("passport")
        setValue('passport', data.passport_id, { shouldValidate: true });
        setShowPassport?.(true);
    }
    if (data.mother_passport) {
        setValue('passport', data.mother_passport, { shouldValidate: true });
        setShowPassport?.(true);
    }

    // --- Date of Birth ---
    if (data.DOB) {
        setValue('dob', data.DOB, { shouldValidate: true });
        const [year, month, day] = data.DOB.split("-");
        setDobYear?.(year || "");
        setDobMonth?.(month || "");
        setDobDay?.(day || "");
    }

    // --- Basic Demographics ---
    if (data.sex) {
        setValue('sex', data.sex, { shouldValidate: true });
    }

    if (data.ethnic) {
        setValue('ethnic', data.ethnic, { shouldValidate: true });
    }

    // --- Mother Information ---
    if (data.mother_fullname) {
        setValue('motherName', data.mother_fullname, { shouldValidate: true });
    }

    if (data.mother_id) {
        setValue('idCard', data.mother_id, { shouldValidate: true });
        setShowIDCard?.(true);
    }

    if (data.mother_address) {
        setValue('address', data.mother_address, { shouldValidate: true });
    }

    if (data.mother_tel) {
        setValue('telephone', data.mother_tel, { shouldValidate: true });
    }

    // --- Contact Person Information ---
    if (data.contact_person_name) {
        setValue('contactName', data.contact_person_name, { shouldValidate: true });
    }

    if (data.relation) {
        setValue('contactRelationship', data.relation, { shouldValidate: true });
    }

    if (data.contact_person_tel) {
        setValue('contactPhone', data.contact_person_tel, { shouldValidate: true });
    }

    if (data.other_contact) {
        setValue('otherContact', data.other_contact, { shouldValidate: true });
    }
};