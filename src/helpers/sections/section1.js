import { toUtcDate } from "../../utils/dateUtils";

// Utility to strip empty/null/undefined fields
const cleanObject = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, v]) => v !== '' && v !== null && v !== undefined)
  );
};

export const mapSection1ToPayload = (form, ctx = {}) => {
  const {
    currentHospital = '',
    TNRPatient = ''
  } = ctx;

  let data = {
    TNR: TNRPatient,
    hospital: currentHospital || 'vernity',
    admission_status: 'admitted',
    admitted_to: form?.admitted_to,
    admission_date: toUtcDate(form?.admission_date, { format: 'noTimezone' }),
    discharge_date: toUtcDate(form?.discharge_date, { format: 'noTimezone' }),
    stay_day: form?.hospital_stay ? String(form.hospital_stay) : undefined,
  };

  // Admission type (inborn/outborn)
  if (form?.admission_type === 'inborn') {
    data.admission_status = 'inborn';
    data.intrauterine_transter = form?.inborn_transfer_type;
  } else if (form?.admission_type === 'outborn') {
    data.admission_status = 'outborn';
    if (form?.outborn_transfer_type === 'tnr') {
      data.transfer_member = 'TNR member hospital';
      data.hospital_name = form?.tnr_hospital_name;
    } else if (form?.outborn_transfer_type === 'non_tnr') {
      data.transfer_member = 'Non TNR member hospital';
      data.non_hospital_name = form?.non_tnr_hospital_name;
    }
  } else if (form?.admission_type === 'bba') {
    data.admission_status = 'BBA';
  }

  // Discharge type
  if (form?.discharge_status === 'discharge_home') {
    data.discharge_type = 'home';
  } else if (form?.discharge_status === 'death') {
    data.discharge_type = 'death';
  } else if (form?.discharge_status === 'refer_tnr') {
    data.discharge_type = 'refer_tnr';
    data.discharge_hospital = form?.discharge_tnr_hospital;
    data.discharge_hospital_code = form?.discharge_tnr_hospital_code;
  } else if (form?.discharge_status === 'refer_level2') {
    data.discharge_type = 'transfer';
    data.discharge_level2_hospital = form?.discharge_level2_hospital;
  }

  // Remove empty values
  data = cleanObject(data);
  return {
    num: '1',
    data
  };
};



export const mapSection1ResponseToFormFields = (data, setters = {}, hospitalOptions = []) => {
  if (!data) return {};

  const {
    setAdmDay,
    setAdmMonth,
    setAdmYear,
    setDisDay,
    setDisMonth,
    setDisYear,
    setValue, // react-hook-form setter
  } = setters;

  const form = {};

  // Basic fields
  form.admitted_to = data.admitted_to || '';
 form.hospital_stay = data.stay_day
  ? `${Number(data.stay_day.replace(/\D/g, ''))} Day(s)`
  : '';


  // --- Admission date ---
  if (data.admission_date) {
    const [datePart] = data.admission_date.split(' ');
    const [yy, mm, dd] = datePart.split('-');
    form.admission_date = `${yy}-${mm}-${dd}`;

    if (setAdmYear) setAdmYear(yy);
    if (setAdmMonth) setAdmMonth(mm);
    if (setAdmDay) setAdmDay(dd);
    if (setValue) setValue('admission_date', form.admission_date, { shouldValidate: true });
  }

  // --- Discharge date ---
  if (data.discharge_date) {
    const [datePart] = data.discharge_date.split(' ');
    const [yy, mm, dd] = datePart.split('-');
    form.discharge_date = `${yy}-${mm}-${dd}`;

    if (setDisYear) setDisYear(yy);
    if (setDisMonth) setDisMonth(mm);
    if (setDisDay) setDisDay(dd);
    if (setValue) setValue('discharge_date', form.discharge_date, { shouldValidate: true });
  }

  // Admission type
  if (data.admission_status === 'inborn') {
    form.admission_type = 'inborn';
    form.inborn_transfer_type = data.intrauterine_transter || '';
  } else if (data.admission_status === 'outborn') {
    form.admission_type = 'outborn';
    if (data.transfer_member === 'TNR member hospital') {
      form.outborn_transfer_type = 'tnr';
      if (setValue) {
        setValue('tnr_hospital_name', data.hospital_name || '', { shouldValidate: true });
        const option = hospitalOptions.find((h) => h.name === data.hospital_name);
        setValue('tnr_hospital_value', option?.value || null, { shouldValidate: true });
      }
    } else if (data.transfer_member === 'Non TNR member hospital') {
      form.outborn_transfer_type = 'non_tnr';
      if (setValue) setValue('non_tnr_hospital_name', data.non_hospital_name || '', { shouldValidate: true });
    }
  } else if (data.admission_status === 'BBA') {
    form.admission_type = 'bba';
  }

  // Discharge type / radio buttons
  if (data.discharge_type === 'home') {
    form.discharge_status = 'discharge_home';
    if (setValue) setValue('discharge_status', 'discharge_home', { shouldValidate: true });
  } else if (data.discharge_type === 'death') {
    form.discharge_status = 'death';
    if (setValue) setValue('discharge_status', 'death', { shouldValidate: true });
  } else if (data.discharge_type === 'refer_tnr') {
    form.discharge_status = 'refer_tnr';
    if (setValue) {
      setValue('discharge_status', 'refer_tnr', { shouldValidate: true });
      setValue('discharge_tnr_hospital', data.discharge_hospital || '', { shouldValidate: true });
      setValue('discharge_tnr_hospital_code', data.discharge_hospital_code || '', { shouldValidate: true });

      // Set dropdown value
      const option = hospitalOptions.find((h) => h.name === data.discharge_hospital);
      setValue('discharge_tnr_hospital_value', option?.value || null, { shouldValidate: true });
    }
  } else if (data.discharge_type === 'transfer') {
    form.discharge_status = 'refer_level2';
    if (setValue) {
      setValue('discharge_status', 'refer_level2', { shouldValidate: true });
      setValue('discharge_level2_hospital', data.discharge_level2_hospital || '', { shouldValidate: true });
    }
  }

  return cleanObject(form);
};





