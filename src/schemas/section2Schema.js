import { z } from 'zod';

export const section2DefaultValues = {
    maternal_age_type: null,
    maternal_age: null,
    gpa_type: null,
    gpa_g: '',
    gpa_p: '',
    gpa_a: '',
    abnormal_serology: [],
    abnormal_serology_other: '',
    complications_pregnancy: [],
    multiple_gestation_type: null,
    multiple_gestation_higher_order_specify: '',
    maternal_drug_abuse_specify: '',
    complications_pregnancy_other: '',
    intrapartum_complications: [],
    intrapartum_complications_other: '',
    gbs_screening: [],
    gbs_screening_other: '',
    maternal_medication: [],
    antibiotics_timing: null,
    prenatal_steroid_course: null,
    maternal_medication_other: [],
};

export const Section2Schema = z.object({
    maternal_age_type: z.enum(['age', 'na']).optional().nullable(),
    maternal_age: z.coerce.number().optional().nullable(),
    gpa_type: z.enum(["g", "p", "a", "na"]).nullable().optional(),
    gpa_g: z.coerce.number().optional(),
    gpa_p: z.coerce.number().optional(),
    gpa_a: z.coerce.number().optional(),
    abnormal_serology: z.array(z.string()),
    abnormal_serology_other: z.string().optional(),
    complications_pregnancy: z.array(z.string()),
    multiple_gestation_type: z.enum(['twin', 'higher_order']).optional().nullable(),
    multiple_gestation_higher_order_specify: z.string().optional(),
    maternal_drug_abuse_specify: z.string().optional(),
    complications_pregnancy_other: z.string().optional(),
    intrapartum_complications: z.array(z.string()),
    intrapartum_complications_other: z.string().optional(),
    gbs_screening: z.array(z.string()),
    gbs_screening_other: z.string().optional(),
    maternal_medication: z.array(z.string()),
    antibiotics_timing: z.enum(['≥ 4 hr prior to delivery', '< 4 hr prior to delivery']).optional().nullable(),
    prenatal_steroid_course: z.enum(['complete course', 'partial course']).optional().nullable(),
    maternal_medication_other: z.array(z.string().nullable()).optional().default([]),
}).superRefine((data, ctx) => {
    if (data.maternal_age_type != 'na' && !data.maternal_age) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['maternal_age'],
            message: 'Maternal age is required',
        });
    }

    // If gpa_type is not null, validate that all GPA fields are filled
    if (data.gpa_type != 'na') {
        if (!data.gpa_g) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['gpa_g'],
                message: 'Gravida (G) is required when GPA type is selected',
            });
        }
        if (!data.gpa_p) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['gpa_p'],
                message: 'Para (P) is required when GPA type is selected',
            });
        }
        if (!data.gpa_a) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['gpa_a'],
                message: 'Abortions (A) is required when GPA type is selected',
            });
        }
    }

    if (data.abnormal_serology.includes('other') && !data.abnormal_serology_other) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['abnormal_serology_other'],
            message: 'Please specify other serology',
        });
    }

    if (data.complications_pregnancy.includes('multiple_gestation') && !data.multiple_gestation_type) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['multiple_gestation_type'],
            message: 'Please specify multiple gestation type',
        });
    }
    if (data.complications_pregnancy.includes('multiple_gestation')) {
        if (data.multiple_gestation_type === 'higher_order' && !data.multiple_gestation_higher_order_specify) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['multiple_gestation_higher_order_specify'],
                message: 'Please specify the higher order gestation',
            });
        }
    }

    if (data.complications_pregnancy.includes('maternal_drug_abuse') && !data.maternal_drug_abuse_specify) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['maternal_drug_abuse_specify'],
            message: 'Please specify the drug abuse',
        });
    }

    if (data.complications_pregnancy.includes('other') && !data.complications_pregnancy_other) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['complications_pregnancy_other'],
            message: 'Please specify other complications',
        });
    }

    if (data.intrapartum_complications.includes('other') && !data.intrapartum_complications_other) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['intrapartum_complications_other'],
            message: 'Please specify other intrapartum complications',
        });
    }

    if (data.gbs_screening.includes('other') && !data.gbs_screening_other) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['gbs_screening_other'],
            message: 'Please specify other organism',
        });
    }
    // Validate maternal medication other fields individually
    if (data.maternal_medication.includes('other')) {
        if (!data.maternal_medication_other || data.maternal_medication_other.length === 0) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['maternal_medication_other'],
                message: 'Please specify at least one other medication',
            });
        } else {
            // Check each individual field for empty values
            data.maternal_medication_other.forEach((medication, index) => {
                if (!medication || medication.trim() === '') {
                    ctx.addIssue({
                        code: z.ZodIssueCode.custom,
                        path: ['maternal_medication_other', index],
                        message: 'Please specify the medication',
                    });
                }
            });
        }
    }

    // If Antibiotics is selected, antibiotics_timing is required
    if (data.maternal_medication.includes('antibiotics') && !data.antibiotics_timing) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['antibiotics_timing'],
            message: 'Please select antibiotics timing',
        });
    }

    // If Dexamethasone/Prenatal Steroid is selected, prenatal_steroid_course is required
    if (data.maternal_medication.includes('dexamethasone_prenatal_steroid') && !data.prenatal_steroid_course) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['prenatal_steroid_course'],
            message: 'Please select prenatal steroid course',
        });
    }

});
